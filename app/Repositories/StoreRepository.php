<?php

namespace App\Repositories;

use App\Contracts\Cartable;
use App\Livewire\Theme\FetchesCart;
use App\Livewire\Theme\FetchesOrder;
use App\Livewire\Theme\FetchesSubscription;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Order;
use App\Models\Product;
use App\Models\Protocol;
use App\Models\Recipe;
use App\Models\RecurringOrder;
use App\Models\Tag;
use App\Models\User;
use App\Models\Vendor;
use App\Support\Enums\ProductType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class StoreRepository
{
    use FetchesCart, FetchesOrder, FetchesSubscription;

    protected ?Cartable $cart;

    protected ?Order $order;

    protected ?RecurringOrder $subscription;

    protected ?int $pricingGroup = null;

    /** @var \Illuminate\Support\Collection<int, int>|null */
    protected ?\Illuminate\Support\Collection $excluded_products = null;

    public function __construct(
        protected Request $request
    ) {
        $this->subscription = $this->fetchCustomerSubscription();
        $this->order = $this->fetchCustomerOrder();
        $this->cart = $this->fetchShopperCart();
    }

    /**
     * @return Builder<Product>
     */
    public function getIndividualProduct(string|int $product_id, string $column = 'slug', bool $onlyShowVisible = true): Builder
    {
        return Product::query()
            ->with([
                'bundle',
                'defaultPrice',
                'prices',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.prices',
                'variants.bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'protocols',
                'category.parentCategory',
                'mainPhoto',
            ])
            ->where($column, $product_id)
            ->when($this->excludedProducts()->isNotEmpty(), fn($q) => $q->excluding($this->excludedProducts()))
            ->when($onlyShowVisible, fn($q) => $q->where('visible', true));
    }

    private function pricingGroupId(): int
    {
        if ( ! is_null($this->pricingGroup)) {
            return $this->pricingGroup;
        }

        if ( ! is_null($this->subscription)) {
            $this->pricingGroup = $this->subscription->pricingGroupId() ?? 0;
            return $this->pricingGroup;
        }

        if ( ! is_null($this->order)) {
            $this->pricingGroup = $this->order->getPricingGroup();
            return $this->pricingGroup;
        }

        if ( ! is_null($this->cart)) {
            $this->pricingGroup = $this->cart->cartPricingGroupId() ?? 0;
            return $this->pricingGroup;
        }

        $this->pricingGroup = 0;

        return $this->pricingGroup;
    }

    /**
     * @return \Illuminate\Support\Collection<int, int>|null
     */
    public function excludedProducts(): ?\Illuminate\Support\Collection
    {
        if ( ! is_null($this->excluded_products)) {
            return $this->excluded_products;
        }

        if ( ! is_null($this->subscription)) {
            $this->excluded_products = $this->subscription->fulfillment?->products()->pluck('products.id');
            return $this->excluded_products;
        }

        if ( ! is_null($this->order)) {
            $this->excluded_products = $this->order->pickup?->products()->pluck('products.id') ?? collect();
            return $this->excluded_products;
        }

        $delivery_method = $this->cart?->cartLocation();

        if ( ! is_null($delivery_method)) {
            $this->excluded_products = $delivery_method->products()->pluck('products.id');
            return $this->excluded_products;
        }

        $this->excluded_products = collect();

        return $this->excluded_products;
    }

    public function getRelatedProducts($relatedProductIds): \Illuminate\Support\Collection
    {
        return Product::query()
            ->with([
                'defaultPrice',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'mainPhoto',
            ])
            ->whereIn('id', explode(',', $relatedProductIds))
            ->orderByRaw('FIELD(id, ' . $relatedProductIds . ')')
            ->get();
    }

    public function getRelatedRecipes(?string $related_recipes): \Illuminate\Support\Collection
    {
        return Recipe::query()
            ->whereIn('slug', explode(',', $related_recipes))
            ->with(['author'])
            ->orderByRaw('FIELD(slug, "' . $related_recipes . '")')
            ->get(['slug', 'title', 'description', 'cover_photo']);
    }

    /**
     * @return Builder<Product>
     */
    public function getDefaultProducts(Request $request): Builder
    {
        return Product::query()
            ->select($this->attributesForSearch())
            ->with([
                'bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'defaultPrice',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }

                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.bundle',
                'mainPhoto',
            ])
            ->where('visible', true)
            ->whereNot('type_id', ProductType::PREORDER->value)
            ->filter($request->all())
            ->when($request->has('q') && ! is_null($request->get('q')) && is_string($request->get('q')), function (Builder $q) use ($request) {
                $q->where('hide_from_search', false);

                if (in_array($request->get('q'), config('grazecart.sale_keywords', []))) {
                    return $q->where('sale', true);
                } else {
                    return $q->basicSearch($request->get('q'));
                }
            }, fn($q) => $q->orderBy(...$this->defaultOrderBy()))
            ->when(
                $this->excludedProducts()->isNotEmpty(),
                fn($q) => $q->excluding($this->excludedProducts())
            );
    }

    protected function attributesForSearch(): array
    {
        $attributes = Product::attributesForSearch();

        unset($attributes['id']);
        $attributes[] = 'products.id';

        return $attributes;
    }

    private function defaultOrderBy(): array
    {
        $ordering = explode('-', setting('store_sort_order', 'title-asc'));

        if (count($ordering) !== 2) {
            return ['title', 'asc'];
        }

        return $ordering;
    }

    public function getFeaturedCollection()
    {
        if (! setting('featured_store_collection')) {
            return null;
        }

        return $this->getCollection(setting('featured_store_collection'), column: 'id');
    }

    public function getCollection(string $id, string $column = 'slug'): ?Collection
    {
        return Collection::query()
            ->select([
                'id', 'title', 'slug', 'description', 'order', 'cover_photo',
                'footer_description', 'settings', 'seo_visibility',
                'canonical_url', 'page_heading', 'page_title',
                'page_description', 'head_tags', 'body_tags',
            ])
            ->with([
                'products' => function ($q) {
                    return $q->select(['products.id'])
                        ->where('visible', true)
                        ->when(
                            $this->excludedProducts()->isNotEmpty(),
                            fn($q) => $q->excluding($this->excludedProducts())
                        );
                }
            ])
            ->where($column, $id)
            ->first();
    }

    /**
     * @return Builder<Product>
     */
    public function getProductsInCollection(Collection $collection, ?Request $request = null)
    {
        return $collection->products()
            ->select($this->attributesForSearch())
            ->with([
                'bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'defaultPrice',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.bundle'
            ])
            ->where('visible', true)
            ->whereNot('type_id', ProductType::PREORDER->value)
            ->when(!is_null($request), fn(Builder $q) => $q->filter($request->all()))
            ->when(
                $this->excludedProducts()->isNotEmpty(),
                fn($q) => $q->excluding($this->excludedProducts())
            )
            ->orderBy($collection->getOrderBy(), $collection->getSort());
    }

    /**
     * @return Builder<Product>
     */
    public function getProductsInCategory(Category $category): Builder
    {
        return Product::query()
            ->select($this->attributesForSearch())
            ->leftJoin('categories as category', 'products.category_id', '=', 'category.id')
            ->leftJoin('categories as parent_category', 'category.category_id', '=', 'parent_category.id')
            ->when(
                is_null($category->category_id),
                fn($q) => $q->whereIn('products.category_id', $category->subcategories->pluck('id')->merge([$category->id])),
                fn($q) => $q->where('products.category_id', $category->id)
            )
            ->where('visible', true)
            ->whereNot('type_id', ProductType::PREORDER->value)
            ->when(
                $this->excludedProducts()->isNotEmpty(),
                fn($q) => $q->excluding($this->excludedProducts())
            )
            ->with([
                'bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'defaultPrice',
                'prices',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.bundle',
                'mainPhoto',
            ])
            ->orderByRaw('-parent_category.position DESC, -category.position DESC, products.category_position ASC');
    }

    /**
     * @return Builder<Product>
     */
    public function getPurchasedProducts(User $user): Builder
    {
        // Get all purchased products with their total quantities
        $purchased_products = DB::table('order_items')
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('orders.customer_id', $user->id)
            ->select('order_items.product_id', DB::raw('SUM(qty) as purchased_count'))
            ->groupBy('order_items.product_id')
            ->orderByDesc('purchased_count')
            ->get();

        // Filter out multi-pack purchases and default to a la carte versions
        $filtered_product_ids = $this->filterMultiPackProducts($purchased_products);

        return Product::query()
            ->whereIn('id', $filtered_product_ids)
            ->select($this->attributesForSearch())
            ->where('visible', true)
            ->whereNot('type_id', ProductType::PREORDER->value)
            ->when(
                $this->excludedProducts()->isNotEmpty(),
                fn($q) => $q->excluding($this->excludedProducts())
            )
            ->with([
                'bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'defaultPrice',
                'prices',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.bundle',
                'mainPhoto',
            ])
            ->orderByRaw("FIELD(id, {$filtered_product_ids->implode(',')})");
    }

    public function categoriesAndProducts(): \Illuminate\Support\Collection
    {
        $categories = Category::query()
            ->whereNull('category_id')
            ->with(['subcategories' => fn($q) => $q->pluck('id')])
            ->orderBy('position')
            ->get();

        return $categories->filter(function (Category $category) {
            return Product::query()
                ->whereIn(
                    'products.category_id',
                    $category->subcategories->pluck('id')->merge([$category->id])
                )
                ->where('visible', true)
                ->whereNot('type_id', ProductType::PREORDER->value)
                ->when(
                    $this->excludedProducts()->isNotEmpty(),
                    fn($q) => $q->excluding($this->excludedProducts())
                )
                ->exists();
        });
    }

    /**
     * @return Builder<Product>
     */
    public function vendor(Request $request): Builder
    {
        return Product::query()
            ->select($this->attributesForSearch())
            ->with([
                'bundle',
                'defaultPrice',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                },
                'variants.defaultPrice',
                'variants.bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'mainPhoto',
            ])
            ->where('visible', true)
            ->filter($request->all())
            ->when($this->excludedProducts()->isNotEmpty(), fn($q) => $q->excluding($this->excludedProducts()))
            ->orderBy($this->defaultOrderByColumn(), $this->defaultSortDirection());
    }

    private function defaultOrderByColumn(): string
    {
        $ordering = explode('-', setting('store_sort_order', 'title-asc'));

        if (count($ordering) !== 2) {
            return 'title';
        }

        return $ordering[0];
    }

    private function defaultSortDirection(): string
    {
        $ordering = explode('-', setting('store_sort_order', 'title-asc'));

        if (count($ordering) !== 2 || !in_array($ordering[1], ['asc', 'desc'])) {
            return 'asc';
        }

        return $ordering[1];
    }

    public function getVendor(string $vendor_slug): ?Vendor
    {
        return Cache::tags(['store', 'collection', 'vendor'])
            ->remember('vendor_' . $vendor_slug, now()->addMinutes(120), function () use ($vendor_slug) {
                return Vendor::query()
                    ->select(['id', 'title', 'description', 'cover_photo', 'visible'])
                    ->with([
                        'products' => function ($q) {
                            return $q->select(['products.id', 'vendor_id'])
                                ->where('visible', true)
                                ->when(
                                    $this->excludedProducts()->isNotEmpty(),
                                    fn($q) => $q->excluding($this->excludedProducts())
                                );
                        },
                        'products.defaultPrice',
                        'products.price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                        'products.variants.price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                        'products.variants.defaultPrice',
                    ])
                    ->where('slug', $vendor_slug)
                    ->first();
            });
    }

    /**
     * @return \Illuminate\Support\Collection<int, Tag>
     */
    public function tags(array $productIds = [], ?string $cacheKey = null): \Illuminate\Support\Collection
    {
        return Cache::tags(['store', 'collection', 'tag', 'product'])
            ->remember('protocol_tags_' . $cacheKey, now()->addMinutes(120), function () use ($productIds) {
                return Tag::query()
                    ->select(['slug', 'title'])
                    ->whereHas('products', fn($q) => $q->whereIn('product_id', $productIds))
                    ->get();
            });
    }

    /**
     * @return Builder<Product>
     */
    public function getProductsForProtocol(Request $request): Builder
    {
        return Product::query()
            ->select($this->attributesForSearch())
            ->with([
                'bundle',
                'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                'defaultPrice',
                'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                'variants' => function ($q) {
                    if ($this->excludedProducts()->isNotEmpty()) {
                        $q->excluding($this->excludedProducts());
                    }
                    $q->with([
                        'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                    ]);
                },
                'variants.defaultPrice',
                'mainPhoto',
            ])
            ->where('visible', true)
            ->whereNot('type_id', ProductType::PREORDER->value)
            ->filter($request->all())
            ->when($this->excludedProducts()->isNotEmpty(), fn($q) => $q->excluding($this->excludedProducts()))
            ->orderBy($this->defaultOrderByColumn(), $this->defaultSortDirection());
    }

    public function getProtocol(string $protocol_slug): ?Protocol
    {
        return Cache::tags(['store', 'collection', 'protocol'])
            ->remember('protocol' . $protocol_slug, now()->addMinutes(120), function () use ($protocol_slug) {
                return Protocol::where('slug', $protocol_slug)
                    ->with(['products' => function ($q) {
                        return $q->select(['products.id', 'products.title'])
                            ->when($this->excludedProducts()->isNotEmpty(), function ($q) {
                                return $q->excluding($this->excludedProducts());
                            })
                            ->where('visible', true);
                    }])->first();
            });
    }

    public function getFeaturedBundles(): \Illuminate\Support\Collection
    {
        return Cache::tags(['store'])
            ->remember('homepage_featured_bundles', now()->addMinutes(120), function () {
                $product_ids = config('app.homepage_featured_bundle_ids', '');

                if(empty($product_ids)) {
                    return collect();
                }

                return Product::query()
                    ->select($this->attributesForSearch())
                    ->with([
                        'bundle',
                        'vendor' => fn($q) => $q->select(['id', 'title', 'slug']),
                        'defaultPrice',
                        'price' => fn($q) => $q->where('group_id', $this->pricingGroupId()),
                        'variants' => function ($q) {
                            $q->with(['price' => fn($q) => $q->where('group_id', $this->pricingGroupId())]);
                        },
                        'variants.defaultPrice',
                        'variants.bundle',
                        'mainPhoto',
                    ])
                    ->whereIn('products.id', explode(',', $product_ids))
                    ->orderByRaw("FIELD(products.id, {$product_ids})")
                    ->get();
            });
    }
}
